import { useState, useEffect, useRef } from 'react';
import {
  Room,
  RoomEvent,
  createLocalVideoTrack,
  createLocalAudioTrack,
  createLocalScreenTracks
} from 'livekit-client';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
  useUploadCbtPaperMutation,
  setStreamingData,
  setIsStreaming,
  setError,
  setChatMessages,
  setChatLoading,
  setChatError,
  clearStreamingData
} from './teacherLiveStreaming.slice';

const TeacherLiveStreaming = () => {
  const dispatch = useDispatch();
  const { isStreaming, streamToken, roomName, error, chatMessages, chatLoading, chatError } =
    useSelector((state) => state.liveStreaming || {});

  const [startStream] = useStartEnhancedStreamMutation();
  const [stopStream] = useStopEnhancedStreamMutation();
  const [sendChatMessageMutation] = useSendChatMessageMutation();
  const [getChatHistory] = useLazyGetChatHistoryQuery();
  const [uploadCbtPaper] = useUploadCbtPaperMutation();

  // LiveKit states
  const [livekitRoom, setLivekitRoom] = useState(null);
  const [livekitConnected, setLivekitConnected] = useState(false);
  const [livekitToken, setLivekitToken] = useState(null);
  const [livekitUrl, setLivekitUrl] = useState(null);
  const [sessionId, setSessionId] = useState(null);

  // Media states
  const [localVideoTrack, setLocalVideoTrack] = useState(null);
  const [localAudioTrack, setLocalAudioTrack] = useState(null);
  const [localScreenTrack, setLocalScreenTrack] = useState(null);
  const [localScreenAudioTrack, setLocalScreenAudioTrack] = useState(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [previewVideoTrack, setPreviewVideoTrack] = useState(null);
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);
  const [screenStream, setScreenStream] = useState(null);

  // UI states
  const [streamStatus, setStreamStatus] = useState('Ready to start streaming');
  const [participants, setParticipants] = useState([]);
  const [quality, setQuality] = useState('medium');
  const [activeSidebarTab, setActiveSidebarTab] = useState('status'); // 'status', 'viewers'

  // Chat states
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [joinedViewers, setJoinedViewers] = useState([]);

  // Socket.IO states
  const [socketConnected, setSocketConnected] = useState(false);

  // Quiz states
  const [showQuizUpload, setShowQuizUpload] = useState(false);
  const [quizFile, setQuizFile] = useState(null);
  const [isUploadingQuiz, setIsUploadingQuiz] = useState(false);

  // Transcription states
  const [isTranscriptionEnabled, setIsTranscriptionEnabled] = useState(false);
  const [speechRecognition, setSpeechRecognition] = useState(null);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [transcriptionStatus, setTranscriptionStatus] = useState('');
  const [microphoneActive, setMicrophoneActive] = useState(false);
  const [livekitTextStream, setLivekitTextStream] = useState(null);

  // Refs
  const videoRef = useRef(null);
  const screenVideoRef = useRef(null);
  const pipCameraRef = useRef(null);
  const livekitRoomRef = useRef(null);
  const sharedMicrophoneStreamRef = useRef(null);
  const recognitionRestartTimer = useRef(null);

  const availableSourceLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' }
  ];

  useEffect(() => {
    if (!window.isSecureContext) {
      console.error('❌ App not running in secure context. Camera access requires HTTPS.');
      setStreamStatus('Error: Camera access requires HTTPS.');
      dispatch(setError('Camera access requires a secure context (HTTPS).'));
    }
  }, []);

  useEffect(() => {
    const userId = sessionStorage.getItem('userId');
    const newSessionId = `teacher_${userId}_${Date.now()}`;
    setSessionId(newSessionId);
  }, []);

  useEffect(() => {
    if (isStreaming) {
      initializeCameraPreview();
    }
    return () => {
      if (!isStreaming && previewVideoTrack) {
        previewVideoTrack.stop();
        setPreviewVideoTrack(null);
      }
    };
  }, [isStreaming]);

  useEffect(() => {
    if (previewVideoTrack && videoRef.current && !isScreenSharing) {
      previewVideoTrack.attach(videoRef.current);
    }
  }, [previewVideoTrack, isScreenSharing]);

  useEffect(() => {
    if (livekitToken && livekitUrl && !livekitRoom) {
      connectToLiveKitRoom();
    }
  }, [livekitToken, livekitUrl]);

  useEffect(() => {
    const publishCameraTrack = async () => {
      if (localVideoTrack && livekitRoom && livekitConnected) {
        try {
          const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
          const existingPublication = publishedTracks.find(
            (pub) => pub.source === 'camera'
          );
          if (!existingPublication) {
            await livekitRoom.localParticipant.publishTrack(localVideoTrack, {
              source: 'camera',
              name: 'teacher_camera'
            });
          }
        } catch (err) {
          console.error('❌ Failed to publish camera track via useEffect:', err);
        }
      }
    };
    publishCameraTrack();
  }, [localVideoTrack, livekitRoom, livekitConnected]);

  useEffect(() => {
    if (screenStream && screenVideoRef.current && isScreenSharing) {
      screenVideoRef.current.srcObject = screenStream;
      screenVideoRef.current.play().catch((err) => {
        console.warn('⚠️ Screen video autoplay failed:', err);
      });
    }
  }, [screenStream, isScreenSharing]);

  useEffect(() => {
    if (isScreenSharing && previewVideoTrack && pipCameraRef.current) {
      try {
        previewVideoTrack.attach(pipCameraRef.current);
      } catch (attachErr) {
        console.warn('⚠️ Failed to attach camera to PiP via useEffect:', attachErr);
      }
    }
  }, [isScreenSharing, previewVideoTrack]);

  // REMOVED: Periodic check for parallel streaming. This is inefficient and a source of instability.
  // Relying on event-driven logic in startScreenShare is the correct approach.

  useEffect(() => {
    if (isStreaming && sessionId) {
      setSocketConnected(true);
      setStreamStatus('Chat system ready');
      loadChatHistory();
      const pollInterval = setInterval(loadChatHistory, 2000);
      return () => {
        clearInterval(pollInterval);
        setSocketConnected(false);
      };
    }
  }, [isStreaming, sessionId]);

  useEffect(() => {
    return () => {
      cleanupAllResources();
    };
  }, []);

  const loadChatHistory = async () => {
    if (!sessionId) return;
    try {
      dispatch(setChatLoading(true));
      dispatch(setChatError(null));
      const result = await getChatHistory(sessionId);
      if (result.data) {
        const newMessages = result.data;
        const currentMessageCount = chatMessages.length;
        if (newMessages.length > currentMessageCount) {
          const newCount = newMessages.length - currentMessageCount;
          if (newCount > 0 && !isChatOpen) {
            setUnreadMessages((prev) => prev + newCount);
          }
        }
        dispatch(setChatMessages(newMessages));
      } else if (result.error) {
        dispatch(setChatError(result.error.data || 'Failed to load chat history'));
      }
    } catch (error) {
      dispatch(setChatError(error.message || 'Failed to load chat history'));
    } finally {
      dispatch(setChatLoading(false));
    }
  };

  const connectToLiveKitRoom = async () => {
    try {
      const room = new Room();
      room.on(RoomEvent.Connected, () => {
        setLivekitConnected(true);
        setStreamStatus('Connected to Live room');
      });

      // MODIFIED: Enhanced disconnect handling to prevent resource leaks.
      room.on(RoomEvent.Disconnected, (reason) => {
        console.log('❌ Disconnected from Live room. Reason:', reason);
        setLivekitConnected(false);
        setStreamStatus('Stream disconnected.');
        
        // Clean up resources and reset state to prevent leaks and ensure stability.
        dispatch(setIsStreaming(false));
        dispatch(clearStreamingData());
        cleanupLiveKitResources();
        cleanupTranscriptionResources();
      });

      room.on(RoomEvent.ParticipantConnected, (participant) => {
        setParticipants((prev) => [...prev, participant]);
        setJoinedViewers((prev) => {
          if (!prev.find((v) => v.viewer_id === participant.identity)) {
            return [...prev, {
              viewer_id: participant.identity,
              viewer_name: participant.name || participant.identity,
              user_role: 'student',
              joined_at: new Date().toISOString(),
              source: 'livekit'
            }];
          }
          return prev;
        });
      });

      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity));
        setJoinedViewers((prev) => prev.filter((v) => v.viewer_id !== participant.identity));
      });

      if (!livekitUrl) throw new Error('LiveKit URL is not set');

      await room.connect(livekitUrl, livekitToken);
      setLivekitRoom(room);
      livekitRoomRef.current = room;

      const cameraTrack = localVideoTrack || previewVideoTrack;
      if (cameraTrack) {
        await room.localParticipant.publishTrack(cameraTrack, { source: 'camera', name: 'teacher_camera' });
        if (!localVideoTrack && previewVideoTrack) setLocalVideoTrack(previewVideoTrack);
      }

      if (sharedMicrophoneStreamRef.current) {
        const audioTrack = await createLocalAudioTrack({
            deviceId: sharedMicrophoneStreamRef.current.getAudioTracks()[0]?.getSettings().deviceId,
            echoCancellation: true,
            noiseSuppression: true
        });
        setLocalAudioTrack(audioTrack);
        await room.localParticipant.publishTrack(audioTrack, { name: 'teacher_microphone', source: 'microphone' });
      } else {
        const audioTrack = await createLocalAudioTrack({ echoCancellation: true, noiseSuppression: true });
        setLocalAudioTrack(audioTrack);
        await room.localParticipant.publishTrack(audioTrack);
      }

    } catch (err) {
      console.error('❌ Failed to connect to Live room:', err);
      setStreamStatus('Failed to connect to Live room');
      dispatch(setError(err.message || 'Failed to connect to Live room'));
    }
  };
  
  const initializeSharedMicrophone = async () => {
    try {
        if (sharedMicrophoneStreamRef.current) return sharedMicrophoneStreamRef.current;
        
        const microphoneStream = await navigator.mediaDevices.getUserMedia({
            audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true }
        });
        sharedMicrophoneStreamRef.current = microphoneStream;
        return microphoneStream;
    } catch (err) {
        console.error('❌ Failed to initialize shared microphone:', err);
        throw err;
    }
  };

  const initializeCameraPreview = async () => {
    try {
      await initializeSharedMicrophone();
      const videoTrack = await createLocalVideoTrack({
        resolution: { width: 640, height: 480 },
        frameRate: 15
      });
      setPreviewVideoTrack(videoTrack);
      setCameraPermissionGranted(true);
      if (videoRef.current) {
        videoTrack.attach(videoRef.current);
      }
    } catch (err) {
      console.error('❌ Error initializing camera preview:', err);
      setCameraPermissionGranted(false);
      let errorMessage = 'Failed to initialize camera preview';
      if (err.name === 'NotAllowedError') errorMessage = 'Camera permission denied.';
      else if (err.name === 'NotFoundError') errorMessage = 'No camera found.';
      else if (err.name === 'NotReadableError') errorMessage = 'Camera in use by another application.';
      setStreamStatus(errorMessage);
      dispatch(setError(errorMessage));
    }
  };

  // MODIFIED: This function is no longer called by a setInterval, removing performance overhead.
  const ensureParallelStreaming = async () => {
    try {
      if (!livekitRoom || !livekitConnected) return;
      const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
      const cameraTrack = localVideoTrack || previewVideoTrack;
      const hasCameraTrack = publishedTracks.some((pub) => pub.source === 'camera');
      if (cameraTrack && !hasCameraTrack) {
        await livekitRoom.localParticipant.publishTrack(cameraTrack, { source: 'camera', name: 'teacher_camera' });
      }
      if (isScreenSharing && localScreenTrack) {
        const hasScreenTrack = publishedTracks.some((pub) => pub.source === 'screen_share');
        if (!hasScreenTrack) {
          await livekitRoom.localParticipant.publishTrack(localScreenTrack, { source: 'screen_share', name: 'teacher_screen' });
        }
      }
    } catch (err) {
      console.error('❌ Failed to ensure parallel streaming:', err);
    }
  };

  const sendChatMessage = async () => {
    if (!newMessage.trim() || !socketConnected || !sessionId) return;
    const messageData = {
      session_id: sessionId,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem('userId'),
      sender_name: sessionStorage.getItem('name') || 'Teacher'
    };
    try {
      await sendChatMessageMutation(messageData).unwrap();
      setNewMessage('');
      setTimeout(loadChatHistory, 500);
    } catch (error) {
      dispatch(setChatError(error.data || 'Failed to send message'));
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) setUnreadMessages(0);
  };

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleGenerateQuiz = () => setShowQuizUpload(true);

  const handleQuizFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') setQuizFile(file);
    else alert('Please select a PDF file');
  };

  const handleUploadQuiz = async () => {
    if (!quizFile || !sessionId) return;
    setIsUploadingQuiz(true);
    try {
      const formData = new FormData();
      formData.append('file', quizFile);
      const response = await uploadCbtPaper(formData).unwrap();
      if (response.object_id) {
        const quizMessage = {
          session_id: sessionId,
          message: `🎯 QUIZ_START:${response.object_id} Quiz Started! Total Questions: ${response.questions?.length || 0}`,
          sender_id: sessionStorage.getItem('userId'),
          sender_name: sessionStorage.getItem('name') || 'Teacher'
        };
        await sendChatMessageMutation(quizMessage).unwrap();
        toast.success(`🎯 Quiz generated and notification sent!`, { position: 'top-right' });
        setShowQuizUpload(false);
        setQuizFile(null);
        setTimeout(loadChatHistory, 500);
      }
    } catch (error) {
      toast.error(`❌ Failed to upload quiz: ${error.data?.message || 'Unknown error'}`, { position: 'top-right' });
    } finally {
      setIsUploadingQuiz(false);
    }
  };

  // ... (getRoleColor, getRoleBadge functions remain the same)

  const startLiveTranscription = async () => {
    try {
      if (isTranscriptionEnabled) return;
      setTranscriptionStatus('🎤 Initializing...');
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) throw new Error('Speech recognition not supported in this browser.');
      
      // Ensure microphone permissions are granted before starting.
      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      setIsTranscriptionEnabled(true);
      await initializeSpeechRecognition();
    } catch (error) {
      console.error('❌ Transcription start error:', error);
      let status = `❌ Error: ${error.message}`;
      if (error.name === 'NotAllowedError') status = '❌ Microphone permission required.';
      setTranscriptionStatus(status);
      setIsTranscriptionEnabled(false);
    }
  };

  const stopLiveTranscription = async () => {
    if (recognitionRestartTimer.current) {
        clearTimeout(recognitionRestartTimer.current);
    }
    if (speechRecognition) {
      speechRecognition.stop();
      setSpeechRecognition(null);
    }
    if (livekitTextStream) {
      await livekitTextStream.close();
      setLivekitTextStream(null);
    }
    setIsTranscriptionEnabled(false);
    setCurrentTranscription('');
    setTranscriptionStatus('');
    setMicrophoneActive(false);
  };
  
  const sendTranscriptionViaLiveKit = async (text) => {
    if (!livekitRoom || !livekitConnected || !text) return;
    try {
      await livekitRoom.localParticipant.sendText(text, { topic: 'transcription' });
    } catch (error) {
      console.error('❌ Failed to send transcription via LiveKit:', error);
    }
  };
  
  // NEW: Robust restart logic with exponential backoff to prevent browser crashes.
  const handleRecognitionRestart = (retryCount = 0) => {
      if (!isTranscriptionEnabled || !speechRecognition) return;
      
      // Clear any previous restart timer
      if (recognitionRestartTimer.current) {
          clearTimeout(recognitionRestartTimer.current);
      }

      // Calculate delay: 2s, 4s, 8s, 16s, max 30s
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
      
      console.log(`🎤 Speech recognition will restart in ${delay / 1000}s (retry #${retryCount})`);
      setTranscriptionStatus(`🎤 Listener paused. Restarting soon...`);

      recognitionRestartTimer.current = setTimeout(() => {
          try {
              if (isTranscriptionEnabled && speechRecognition) {
                  // It's often safer to create a new instance on restart
                  initializeSpeechRecognition(retryCount + 1);
              }
          } catch (err) {
              console.error('Failed to restart recognition:', err);
              setTranscriptionStatus(`❌ Restart failed: ${err.message}`);
          }
      }, delay);
  };

  const initializeSpeechRecognition = async (retryCount = 0) => {
    try {
        if (speechRecognition) {
            speechRecognition.onend = null;
            speechRecognition.onerror = null;
            speechRecognition.stop();
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US'; // Or map from sourceLanguage

        recognition.onstart = () => {
            console.log('🎤 Speech recognition STARTED');
            setTranscriptionStatus('🎤 Listening...');
        };

        recognition.onresult = (event) => {
            const result = event.results[event.results.length - 1];
            const transcript = result[0].transcript;
            setCurrentTranscription(transcript);
            if (result.isFinal && transcript.trim()) {
                sendTranscriptionViaLiveKit(transcript.trim());
            }
        };

        recognition.onaudiostart = () => setMicrophoneActive(true);
        recognition.onaudioend = () => setMicrophoneActive(false);

        // MODIFIED: Use the new stable restart handler
        recognition.onend = () => {
            console.log('🎤 Speech recognition session ended.');
            if (isTranscriptionEnabled) {
                handleRecognitionRestart(retryCount);
            }
        };
        
        // MODIFIED: Use the new stable restart handler for errors too
        recognition.onerror = (event) => {
            console.error(`🎤 Speech recognition error: ${event.error}`);
            if (event.error === 'no-speech' || event.error === 'network' || event.error === 'audio-capture') {
                if (isTranscriptionEnabled) {
                    handleRecognitionRestart(retryCount);
                }
            } else if (event.error === 'not-allowed') {
                setTranscriptionStatus("❌ Microphone permission denied.");
                stopLiveTranscription();
            } else {
                 setTranscriptionStatus(`❌ Error: ${event.error}`);
            }
        };

        recognition.start();
        setSpeechRecognition(recognition);
    } catch (error) {
        setTranscriptionStatus(`❌ Error: ${error.message}`);
        console.error('❌ Speech recognition initialization error:', error);
    }
  };


  const startStreaming = async () => {
    try {
      if (!previewVideoTrack && !localVideoTrack) {
        await initializeCameraPreview();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      if (!sharedMicrophoneStreamRef.current) {
        await initializeSharedMicrophone();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      
      const userId = sessionStorage.getItem('userId');
      const response = await startStream({ userId, sessionId, quality, screenShareEnabled: isScreenSharing }).unwrap();
      const backendSessionId = response.session_id;

      if (backendSessionId) {
        setSessionId(backendSessionId);
        sessionStorage.setItem('streamSessionId', backendSessionId);
      }

      dispatch(setStreamingData({ streamToken: response.livekit_token, roomName: response.roomName }));
      dispatch(setIsStreaming(true));
      setLivekitToken(response.livekit_token);
      setLivekitUrl(response.livekit_url);
      if (previewVideoTrack) setLocalVideoTrack(previewVideoTrack);
      setStreamStatus('LiveKit stream started successfully');

    } catch (err) {
      console.error('❌ Failed to start stream:', err);
      setStreamStatus('Failed to start stream');
      dispatch(setError(err.message || 'Failed to start stream'));
    }
  };

  const stopStreaming = async () => {
    try {
      const storedSessionId = sessionStorage.getItem('streamSessionId');
      if (storedSessionId) {
          await stopStream({ session_id: storedSessionId }).unwrap();
          sessionStorage.removeItem('streamSessionId');
      }
      
      cleanupAllResources();
      dispatch(setIsStreaming(false));
      dispatch(clearStreamingData());
      setStreamStatus('Ready to start streaming');
    } catch (err) {
      console.error('❌ Failed to stop stream:', err);
      dispatch(setError(err.message || 'Failed to stop stream'));
    }
  };
  
  const cleanupLiveKitResources = () => {
    localVideoTrack?.stop();
    localAudioTrack?.stop();
    previewVideoTrack?.stop();
    localScreenTrack?.stop();
    localScreenAudioTrack?.stop();
    screenStream?.getTracks().forEach((track) => track.stop());
    
    setLocalVideoTrack(null);
    setLocalAudioTrack(null);
    setPreviewVideoTrack(null);
    setLocalScreenTrack(null);
    setLocalScreenAudioTrack(null);
    setScreenStream(null);

    if (livekitRoomRef.current) {
      livekitRoomRef.current.disconnect();
      livekitRoomRef.current = null;
    }
    setLivekitRoom(null);
    setLivekitConnected(false);
  };

  const cleanupTranscriptionResources = () => {
    if (recognitionRestartTimer.current) clearTimeout(recognitionRestartTimer.current);
    if (speechRecognition) {
      speechRecognition.stop();
      setSpeechRecognition(null);
    }
    setIsTranscriptionEnabled(false);
    setCurrentTranscription('');
    setTranscriptionStatus('');
  };

  const cleanupSharedMicrophone = () => {
    if (sharedMicrophoneStreamRef.current) {
      sharedMicrophoneStreamRef.current.getTracks().forEach((track) => track.stop());
      sharedMicrophoneStreamRef.current = null;
    }
  };
  
  const cleanupAllResources = () => {
    cleanupLiveKitResources();
    cleanupTranscriptionResources();
    cleanupSharedMicrophone();
    setIsScreenSharing(false);
    setParticipants([]);
    setJoinedViewers([]);
  };

  const startScreenShare = async () => {
    try {
      setStreamStatus('Starting screen share...');
      const screenTracks = await createLocalScreenTracks({ audio: true, video: true });
      const screenVideoTrack = screenTracks.find((track) => track.kind === 'video');
      const screenAudioTrack = screenTracks.find((track) => track.kind === 'audio');

      if (screenVideoTrack) {
        setLocalScreenTrack(screenVideoTrack);
        if (screenAudioTrack) setLocalScreenAudioTrack(screenAudioTrack);

        // Get the native MediaStream for the video element
        const screenMediaStream = new MediaStream([screenVideoTrack.mediaStreamTrack]);
        setScreenStream(screenMediaStream);
        
        screenVideoTrack.on('ended', () => stopScreenShare());

        setIsScreenSharing(true);
        setStreamStatus('Screen sharing active');

        if (previewVideoTrack && pipCameraRef.current) {
          previewVideoTrack.detach(videoRef.current);
          previewVideoTrack.attach(pipCameraRef.current);
        }

        if (livekitRoom && livekitConnected) {
          await livekitRoom.localParticipant.publishTrack(screenVideoTrack, { source: 'screen_share' });
          if (screenAudioTrack) await livekitRoom.localParticipant.publishTrack(screenAudioTrack, { source: 'screen_share_audio' });
          // Ensure camera is also published
          ensureParallelStreaming();
        }
      }
    } catch (err) {
      console.error('❌ Error starting screen share:', err);
      let errorMessage = 'Failed to start screen share';
      if (err.name === 'NotAllowedError') errorMessage = 'Screen share permission denied.';
      setStreamStatus(errorMessage);
      setIsScreenSharing(false);
    }
  };

  const stopScreenShare = async () => {
    try {
      setStreamStatus('Stopping screen share...');
      if (livekitRoom && livekitConnected) {
        if (localScreenTrack) await livekitRoom.localParticipant.unpublishTrack(localScreenTrack);
        if (localScreenAudioTrack) await livekitRoom.localParticipant.unpublishTrack(localScreenAudioTrack);
      }
      
      localScreenTrack?.stop();
      setLocalScreenTrack(null);
      localScreenAudioTrack?.stop();
      setLocalScreenAudioTrack(null);
      
      setIsScreenSharing(false);
      setStreamStatus('Live');
      
      if (previewVideoTrack && videoRef.current) {
        previewVideoTrack.detach(pipCameraRef.current);
        previewVideoTrack.attach(videoRef.current);
      }
    } catch (err) {
      console.error('❌ Error stopping screen share:', err);
      setStreamStatus('Error stopping screen share');
    }
  };

  
import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const TeacherLiveStreaming = () => {
  // Core streaming states
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamToken, setStreamToken] = useState(null);
  const [roomName, setRoomName] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [error, setError] = useState(null);

  // LiveKit states
  const livekitRoomRef = useRef(null);
  const [livekitConnected, setLivekitConnected] = useState(false);
  const [livekitToken, setLivekitToken] = useState(null);
  const [livekitUrl, setLivekitUrl] = useState('wss://demo.livekit.io');
  const [connectionState, setConnectionState] = useState('disconnected');

  // Media states
  const localVideoTrackRef = useRef(null);
  const localAudioTrackRef = useRef(null);
  const localScreenTrackRef = useRef(null);
  const localScreenAudioTrackRef = useRef(null);
  const sharedMicrophoneStreamRef = useRef(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [previewVideoTrack, setPreviewVideoTrack] = useState(null);
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);
  const [screenStream, setScreenStream] = useState(null);

  // UI states
  const [streamStatus, setStreamStatus] = useState('Ready to start streaming');
  const [participants, setParticipants] = useState([]);
  const [quality, setQuality] = useState('medium');
  const [activeSidebarTab, setActiveSidebarTab] = useState('status');

  // Chat states
  const [chatMessages, setChatMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [joinedViewers, setJoinedViewers] = useState([]);
  const [socketConnected, setSocketConnected] = useState(false);

  // Quiz states
  const [showQuizUpload, setShowQuizUpload] = useState(false);
  const [quizFile, setQuizFile] = useState(null);
  const [isUploadingQuiz, setIsUploadingQuiz] = useState(false);

  // Transcription states
  const [isTranscriptionEnabled, setIsTranscriptionEnabled] = useState(false);
  const speechRecognitionRef = useRef(null);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [transcriptionStatus, setTranscriptionStatus] = useState('');
  const [microphoneActive, setMicrophoneActive] = useState(false);

  // Refs for stable references
  const videoRef = useRef(null);
  const screenVideoRef = useRef(null);
  const pipCameraRef = useRef(null);
  const recognitionRestartTimerRef = useRef(null);
  const chatPollingIntervalRef = useRef(null);
  const tokenRefreshIntervalRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const reconnectionTimeoutRef = useRef(null);

  // Page visibility and tab switching handling
  const [isPageVisible, setIsPageVisible] = useState(true);
  const [tabSwitchCount, setTabSwitchCount] = useState(0);

  const availableSourceLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' }
  ];

  // Mock data for demo
  const mockViewers = [
    { viewer_id: '1', viewer_name: 'Alice Smith', user_role: 'student', joined_at: new Date().toISOString() },
    { viewer_id: '2', viewer_name: 'Bob Johnson', user_role: 'student', joined_at: new Date().toISOString() },
    { viewer_id: '3', viewer_name: 'Carol Wilson', user_role: 'parent', joined_at: new Date().toISOString() }
  ];

  const mockChatMessages = [
    { id: '1', message: 'Welcome to the live stream!', sender_name: 'Teacher', timestamp: new Date().toISOString() },
    { id: '2', message: 'Thank you for joining us today', sender_name: 'Teacher', timestamp: new Date().toISOString() },
    { id: '3', message: 'Can you explain the last concept again?', sender_name: 'Alice', timestamp: new Date().toISOString() }
  ];

  // Initialize session ID once
  useEffect(() => {
    const userId = 'demo_teacher_123';
    const newSessionId = `teacher_${userId}_${Date.now()}`;
    setSessionId(newSessionId);
    
    // Initialize mock data
    setJoinedViewers(mockViewers);
    setChatMessages(mockChatMessages);
  }, []);

  // Page visibility handling
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      setIsPageVisible(isVisible);
      
      if (!isVisible) {
        setTabSwitchCount(prev => prev + 1);
        console.log('📱 Tab switched away - maintaining stream connection');
      } else {
        console.log('📱 Tab visible again - restoring full quality');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isStreaming]);

  // Initialize camera preview
  const initializeCameraPreview = useCallback(async () => {
    try {
      console.log('📹 Initializing camera preview...');
      
      if (previewVideoTrack && !previewVideoTrack.ended) {
        console.log('✅ Camera preview already active');
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          width: quality === 'high' ? 1920 : quality === 'low' ? 640 : 1280, 
          height: quality === 'high' ? 1080 : quality === 'low' ? 360 : 720,
          facingMode: 'user'
        },
        audio: { 
          echoCancellation: true, 
          noiseSuppression: true, 
          autoGainControl: true 
        }
      });

      const videoTrack = stream.getVideoTracks()[0];
      const audioTrack = stream.getAudioTracks()[0];

      setPreviewVideoTrack(stream);
      setCameraPermissionGranted(true);
      
      if (videoRef.current && !isScreenSharing) {
        videoRef.current.srcObject = stream;
      }

      // Store tracks for later use
      if (!localVideoTrackRef.current) {
        localVideoTrackRef.current = videoTrack;
      }
      if (!localAudioTrackRef.current) {
        localAudioTrackRef.current = audioTrack;
      }

      console.log('✅ Camera preview initialized successfully');
      
    } catch (err) {
      console.error('❌ Error initializing camera preview:', err);
      setCameraPermissionGranted(false);
      
      let errorMessage = 'Failed to initialize camera preview';
      if (err.name === 'NotAllowedError') errorMessage = 'Camera permission denied.';
      else if (err.name === 'NotFoundError') errorMessage = 'No camera found.';
      else if (err.name === 'NotReadableError') errorMessage = 'Camera in use by another application.';
      
      setStreamStatus(errorMessage);
      setError(errorMessage);
    }
  }, [quality, previewVideoTrack, isScreenSharing]);

  // Speech recognition functions
  const startLiveTranscription = useCallback(async () => {
    try {
      if (isTranscriptionEnabled) return;
      
      console.log('🎤 Starting live transcription...');
      setTranscriptionStatus('🎤 Initializing...');
      
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        throw new Error('Speech recognition not supported in this browser.');
      }

      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      setIsTranscriptionEnabled(true);
      await initializeSpeechRecognition();
      
    } catch (error) {
      console.error('❌ Transcription start error:', error);
      let status = `❌ Error: ${error.message}`;
      if (error.name === 'NotAllowedError') status = '❌ Microphone permission required.';
      setTranscriptionStatus(status);
      setIsTranscriptionEnabled(false);
    }
  }, [isTranscriptionEnabled]);

  const stopLiveTranscription = useCallback(async () => {
    console.log('🛑 Stopping live transcription...');
    
    if (recognitionRestartTimerRef.current) {
      clearTimeout(recognitionRestartTimerRef.current);
      recognitionRestartTimerRef.current = null;
    }
    
    if (speechRecognitionRef.current) {
      try {
        speechRecognitionRef.current.onend = null;
        speechRecognitionRef.current.onerror = null;
        speechRecognitionRef.current.stop();
      } catch (e) {
        console.warn('⚠️ Error stopping speech recognition:', e);
      }
      speechRecognitionRef.current = null;
    }
    
    setIsTranscriptionEnabled(false);
    setCurrentTranscription('');
    setTranscriptionStatus('');
    setMicrophoneActive(false);
    
    console.log('✅ Live transcription stopped');
  }, []);

  const initializeSpeechRecognition = useCallback(async () => {
    try {
      if (speechRecognitionRef.current) {
        speechRecognitionRef.current.onend = null;
        speechRecognitionRef.current.onerror = null;
        speechRecognitionRef.current.stop();
      }

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = sourceLanguage === 'en' ? 'en-US' : 
                       sourceLanguage === 'ta' ? 'ta-IN' :
                       sourceLanguage === 'hi' ? 'hi-IN' :
                       sourceLanguage === 'te' ? 'te-IN' :
                       sourceLanguage === 'kn' ? 'kn-IN' : 'en-US';

      let isRestarting = false;
      let lastRestartTime = 0;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition STARTED');
        setTranscriptionStatus('🎤 Listening...');
        setMicrophoneActive(true);
      };

      recognition.onresult = (event) => {
        try {
          const result = event.results[event.results.length - 1];
          const transcript = result[0].transcript;
          setCurrentTranscription(transcript);
          
          if (result.isFinal && transcript.trim()) {
            console.log('Final transcript:', transcript.trim());
          }
        } catch (e) {
          console.warn('⚠️ Error processing speech result:', e);
        }
      };

      recognition.onaudiostart = () => setMicrophoneActive(true);
      recognition.onaudioend = () => setMicrophoneActive(false);

      recognition.onend = () => {
        console.log('🎤 Speech recognition ended');
        setMicrophoneActive(false);
        
        if (isTranscriptionEnabled && !isRestarting) {
          const now = Date.now();
          const timeSinceLastRestart = now - lastRestartTime;
          
          if (timeSinceLastRestart > 3000) {
            lastRestartTime = now;
            isRestarting = true;
            
            console.log('🔄 Scheduling recognition restart...');
            setTranscriptionStatus('🎤 Restarting...');
            
            recognitionRestartTimerRef.current = setTimeout(() => {
              if (isTranscriptionEnabled) {
                try {
                  recognition.start();
                  isRestarting = false;
                } catch (e) {
                  console.error('❌ Failed to restart recognition:', e);
                  isRestarting = false;
                  setTranscriptionStatus(`❌ Restart failed: ${e.message}`);
                }
              }
            }, 1000);
          } else {
            console.log('⚠️ Recognition restart rate limited');
            setTranscriptionStatus('⚠️ Restart rate limited');
          }
        }
      };

      recognition.onerror = (event) => {
        console.error(`🎤 Speech recognition error: ${event.error}`);
        setMicrophoneActive(false);
        
        if (event.error === 'no-speech' || event.error === 'audio-capture') {
          if (isTranscriptionEnabled && !isRestarting) {
            setTranscriptionStatus('🎤 No speech detected, continuing...');
          }
        } else if (event.error === 'not-allowed') {
          setTranscriptionStatus("❌ Microphone permission denied.");
          setIsTranscriptionEnabled(false);
        } else if (event.error === 'network') {
          setTranscriptionStatus('⚠️ Network error, will retry...');
        } else {
          setTranscriptionStatus(`❌ Error: ${event.error}`);
        }
      };

      recognition.start();
      speechRecognitionRef.current = recognition;
      
    } catch (error) {
      setTranscriptionStatus(`❌ Error: ${error.message}`);
      console.error('❌ Speech recognition initialization error:', error);
    }
  }, [sourceLanguage, isTranscriptionEnabled]);

  // Streaming functions
  const startStreaming = useCallback(async () => {
    try {
      console.log('🚀 Starting streaming...');
      setStreamStatus('Initializing stream...');

      if (!previewVideoTrack && !localVideoTrackRef.current) {
        await initializeCameraPreview();
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // Mock stream start - in real implementation, this would call your backend
      const mockResponse = {
        livekit_token: 'mock_token_' + Date.now(),
        livekit_url: 'wss://demo.livekit.io',
        session_id: sessionId
      };

      console.log('✅ Stream started, received mock credentials');
      
      setStreamToken(mockResponse.livekit_token);
      setRoomName(mockResponse.session_id);
      setIsStreaming(true);
      
      setLivekitToken(mockResponse.livekit_token);
      setLivekitUrl(mockResponse.livekit_url);
      
      // Mock connection
      setTimeout(() => {
        setLivekitConnected(true);
        setConnectionState('connected');
        setSocketConnected(true);
        setStreamStatus('Stream started successfully (Demo Mode)');
      }, 2000);
      
    } catch (err) {
      console.error('❌ Failed to start stream:', err);
      setStreamStatus('Failed to start stream');
      setError(err.message || 'Failed to start stream');
    }
  }, [previewVideoTrack, initializeCameraPreview, sessionId]);

  const stopStreaming = useCallback(async () => {
    try {
      console.log('🛑 Stopping streaming...');
      setStreamStatus('Stopping stream...');

      await stopLiveTranscription();
      
      // Clean up resources
      if (previewVideoTrack) {
        previewVideoTrack.getTracks().forEach(track => track.stop());
        setPreviewVideoTrack(null);
      }

      if (screenStream) {
        screenStream.getTracks().forEach(track => track.stop());
        setScreenStream(null);
      }

      // Reset states
      setIsStreaming(false);
      setLivekitConnected(false);
      setConnectionState('disconnected');
      setSocketConnected(false);
      setIsScreenSharing(false);
      setStreamStatus('Ready to start streaming');
      
      console.log('✅ Stream stopped and cleaned up');
      
    } catch (err) {
      console.error('❌ Error stopping stream:', err);
      setError(err.message || 'Failed to stop stream');
    }
  }, [stopLiveTranscription, previewVideoTrack, screenStream]);

  // Screen sharing functions
  const startScreenShare = useCallback(async () => {
    try {
      console.log('🖥️ Starting screen share...');
      setStreamStatus('Starting screen share...');
      
      const stream = await navigator.mediaDevices.getDisplayMedia({ 
        video: { width: 1920, height: 1080 },
        audio: true
      });
      
      setScreenStream(stream);
      setIsScreenSharing(true);
      setStreamStatus('Screen sharing active');

      if (screenVideoRef.current) {
        screenVideoRef.current.srcObject = stream;
      }

      // Move camera to PiP
      if (previewVideoTrack && pipCameraRef.current) {
        pipCameraRef.current.srcObject = previewVideoTrack;
      }

      // Handle screen share ended
      stream.getVideoTracks()[0].onended = () => {
        console.log('🖥️ Screen share ended by user');
        stopScreenShare();
      };

    } catch (err) {
      console.error('❌ Error starting screen share:', err);
      let errorMessage = 'Failed to start screen share';
      if (err.name === 'NotAllowedError') errorMessage = 'Screen share permission denied.';
      setStreamStatus(errorMessage);
      setIsScreenSharing(false);
    }
  }, [previewVideoTrack]);

  const stopScreenShare = useCallback(async () => {
    try {
      console.log('🛑 Stopping screen share...');
      setStreamStatus('Stopping screen share...');
      
      if (screenStream) {
        screenStream.getTracks().forEach(track => track.stop());
        setScreenStream(null);
      }
      
      setIsScreenSharing(false);
      setStreamStatus('Live');
      
      // Move camera back to main view
      if (previewVideoTrack && videoRef.current) {
        videoRef.current.srcObject = previewVideoTrack;
      }
      
      console.log('✅ Screen share stopped');
    } catch (err) {
      console.error('❌ Error stopping screen share:', err);
      setStreamStatus('Error stopping screen share');
    }
  }, [previewVideoTrack, screenStream]);

  // Chat functions
  const sendChatMessage = useCallback(async () => {
    if (!newMessage.trim() || !socketConnected || !sessionId) return;
    
    const message = {
      id: Date.now().toString(),
      message: newMessage.trim(),
      sender_name: 'Teacher',
      timestamp: new Date().toISOString()
    };
    
    setChatMessages(prev => [...prev, message]);
    setNewMessage('');
  }, [newMessage, socketConnected, sessionId]);

  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  }, [sendChatMessage]);

  const toggleChat = useCallback(() => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) setUnreadMessages(0);
  }, [isChatOpen]);

  const formatMessageTime = useCallback((timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Quiz functions
  const handleGenerateQuiz = useCallback(() => setShowQuizUpload(true), []);

  const handleQuizFileChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
      setQuizFile(file);
    } else {
      alert('Please select a PDF file');
    }
  }, []);

  const handleUploadQuiz = useCallback(async () => {
    if (!quizFile || !sessionId) return;
    
    setIsUploadingQuiz(true);
    try {
      // Mock quiz upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const quizMessage = {
        id: Date.now().toString(),
        message: `🎯 Quiz Started! Total Questions: 10`,
        sender_name: 'Teacher',
        timestamp: new Date().toISOString()
      };
      
      setChatMessages(prev => [...prev, quizMessage]);
      setShowQuizUpload(false);
      setQuizFile(null);
      alert('🎯 Quiz generated and notification sent!');
    } catch (error) {
      alert(`❌ Failed to upload quiz: ${error.message}`);
    } finally {
      setIsUploadingQuiz(false);
    }
  }, [quizFile, sessionId]);

  // Helper functions
  const getRoleColor = useCallback((role) => {
    switch (role) {
      case 'faculty':
      case 'kota_teacher':
        return 'text-purple-600';
      case 'student':
        return 'text-blue-600';
      case 'parent':
        return 'text-green-600';
      case 'center_counselor':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  }, []);

  const getRoleBadge = useCallback((role) => {
    switch (role) {
      case 'faculty':
        return '👨‍🏫 Faculty';
      case 'kota_teacher':
        return '🎓 Teacher';
      case 'student':
        return '👨‍🎓 Student';
      case 'parent':
        return '👨‍👩‍👧‍👦 Parent';
      case 'center_counselor':
        return '💼 Counselor';
      default:
        return '👤 User';
    }
  }, []);

  // Initialize camera when streaming starts
  useEffect(() => {
    if (isStreaming && !previewVideoTrack) {
      initializeCameraPreview();
    }
  }, [isStreaming, previewVideoTrack, initializeCameraPreview]);
return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute top-10 left-10 w-32 h-32 bg-blue-200/30 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            x: [0, -150, 0],
            y: [0, 100, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute top-1/2 right-20 w-40 h-40 bg-indigo-200/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            x: [0, 80, 0],
            y: [0, -80, 0]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute bottom-20 left-1/3 w-24 h-24 bg-purple-200/25 rounded-full blur-xl"
        />
      </div>

      <div className="max-w-8xl mx-auto p-4 sm:p-6 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30 p-8 mb-8 relative overflow-hidden"
        >
          {/* Header background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-indigo-50/30 to-purple-50/50 rounded-3xl" />

          <div className="relative z-10 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            <div className="flex items-center space-x-4">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className="w-16 h-16 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"
              >
                <motion.svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </motion.svg>
              </motion.div>
              <div>
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                  className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent"
                >
                  Live Streaming Studio
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="text-gray-600 text-base font-medium mt-1"
                >
                  Professional Teaching Platform ✨
                </motion.p>
              </div>
            </div>
            {isStreaming && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="flex flex-col sm:flex-row items-start sm:items-center gap-4"
              >
                <motion.select
                  whileHover={{ scale: 1.02 }}
                  whileFocus={{ scale: 1.02 }}
                  value={quality}
                  onChange={(e) => setQuality(e.target.value)}
                  className="px-5 py-3 bg-white/80 border border-gray-200/50 rounded-2xl focus:outline-none focus:ring-3 focus:ring-blue-500/30 focus:border-blue-400 transition-all duration-300 shadow-lg backdrop-blur-sm font-medium"
                >
                  <option value="low">🔹 Low Quality</option>
                  <option value="medium">🔸 Medium Quality</option>
                  <option value="high">🔶 High Quality</option>
                </motion.select>

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                  onClick={handleGenerateQuiz}
                  className="px-8 py-3 bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 text-white rounded-2xl hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl font-semibold relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative flex items-center space-x-3">
                    <motion.svg
                      className="w-5 h-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      whileHover={{ rotate: 15 }}
                      transition={{ duration: 0.2 }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </motion.svg>
                    <span>Generate Quiz</span>
                  </span>
                </motion.button>

                {/* Test Quiz Button - HIDDEN FOR PRODUCTION */}
                {/* <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={sendTestQuizMessage}
                  className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium text-sm"
                >
                  <span className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    <span>Test Quiz</span>
                  </span>
                </motion.button> */}

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                  onClick={stopStreaming}
                  className="px-8 py-3 bg-gradient-to-r from-red-500 via-red-600 to-pink-600 text-white rounded-2xl hover:from-red-600 hover:via-red-700 hover:to-pink-700 transition-all duration-300 shadow-xl hover:shadow-2xl font-semibold relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative flex items-center space-x-3">
                    <motion.span
                      className="w-3 h-3 bg-white rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                    <span>Stop Streaming</span>
                  </span>
                </motion.button>
              </motion.div>
            )}
          </div>

          {/* Enhanced Transcription Controls */}
          {isStreaming && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2, ease: 'easeOut' }}
              className="mt-6 p-6 bg-gradient-to-br from-purple-50/80 via-indigo-50/60 to-blue-50/80 backdrop-blur-sm border border-purple-200/50 rounded-3xl shadow-lg relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-indigo-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16" />

              <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
                  <div className="flex items-center space-x-3">
                    <motion.div
                      animate={microphoneActive ? { scale: [1, 1.1, 1] } : {}}
                      transition={{ duration: 0.5, repeat: microphoneActive ? Infinity : 0 }}
                      className={`p-2 rounded-xl ${microphoneActive ? 'bg-green-100' : 'bg-purple-100'} transition-colors duration-300`}
                    >
                      <svg
                        className={`w-6 h-6 ${microphoneActive ? 'text-green-600' : 'text-purple-600'} transition-colors duration-300`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                        />
                      </svg>
                    </motion.div>
                    <div>
                      <span className="text-purple-800 font-semibold text-lg">
                        Live Transcription
                      </span>
                      {microphoneActive && (
                        <motion.div
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-green-600 text-sm font-medium"
                        >
                          🎤 Listening...
                        </motion.div>
                      )}
                    </div>
                  </div>

                  <motion.select
                    whileHover={{ scale: 1.02 }}
                    whileFocus={{ scale: 1.02 }}
                    value={sourceLanguage}
                    onChange={(e) => setSourceLanguage(e.target.value)}
                    className="px-4 py-2 bg-white/80 border border-purple-300/50 rounded-xl text-sm focus:outline-none focus:ring-3 focus:ring-purple-500/30 focus:border-purple-400 transition-all duration-300 shadow-md backdrop-blur-sm font-medium"
                    disabled={isTranscriptionEnabled}
                  >
                    {availableSourceLanguages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </motion.select>

                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                    onClick={
                      isTranscriptionEnabled ? stopLiveTranscription : startLiveTranscription
                    }
                    className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden group ${
                      isTranscriptionEnabled
                        ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white'
                        : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white'
                    }`}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <span className="relative flex items-center space-x-2">
                      {isTranscriptionEnabled ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                            className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                          />
                          <span>Stop Transcription</span>
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728"
                            />
                          </svg>
                          <span>Start Transcription</span>
                        </>
                      )}
                    </span>
                  </motion.button>

                  {/* Debug button for testing microphone - HIDDEN FOR PRODUCTION */}
                  {/* <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={async () => {
                      try {
                        console.log('🎤 Testing microphone and speech recognition...');
                        setTranscriptionStatus('🎤 Testing microphone...');

                        // Test microphone access
                        const stream = await navigator.mediaDevices.getUserMedia({
                          audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false
                          }
                        });
                        console.log('🎤 Microphone access successful');

                        // Test audio levels
                        const audioContext = new (window.AudioContext ||
                          window.webkitAudioContext)();
                        const source = audioContext.createMediaStreamSource(stream);
                        const analyser = audioContext.createAnalyser();
                        source.connect(analyser);

                        const dataArray = new Uint8Array(analyser.frequencyBinCount);

                        let maxLevel = 0;
                        const checkAudio = () => {
                          analyser.getByteFrequencyData(dataArray);
                          const level = dataArray.reduce((a, b) => a + b) / dataArray.length;
                          maxLevel = Math.max(maxLevel, level);
                        };

                        // Check audio for 3 seconds
                        const interval = setInterval(checkAudio, 100);
                        setTimeout(() => {
                          clearInterval(interval);
                          stream.getTracks().forEach((track) => track.stop());
                          audioContext.close();

                          console.log('🎤 Max audio level detected:', maxLevel);
                          if (maxLevel > 5) {
                            setTranscriptionStatus(
                              `✅ Microphone working! Max level: ${maxLevel.toFixed(1)}`
                            );
                          } else {
                            setTranscriptionStatus(
                              `⚠️ Low audio level: ${maxLevel.toFixed(1)} - speak louder`
                            );
                          }
                        }, 3000);

                        setTranscriptionStatus('🎤 Speak now for 3 seconds to test...');
                      } catch (error) {
                        console.error('🎤 Microphone test failed:', error);
                        setTranscriptionStatus(`❌ Test failed: ${error.message}`);
                      }
                    }}
                    className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded-lg"
                  >
                    Test Mic
                  </motion.button> */}

                  {/* Force restart speech recognition */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={async () => {
                      try {
                        console.log('🎤 Force restarting speech recognition...');
                        setTranscriptionStatus('🎤 Force restarting...');

                        // Stop current recognition
                        if (speechRecognition) {
                          speechRecognition.stop();
                          setSpeechRecognition(null);
                        }

                        // Wait and restart
                        await new Promise((resolve) => setTimeout(resolve, 1000));
                        await initializeSpeechRecognition();
                      } catch (error) {
                        console.error('🎤 Force restart failed:', error);
                        setTranscriptionStatus(`❌ Restart failed: ${error.message}`);
                      }
                    }}
                    className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg"
                    disabled={!isTranscriptionEnabled}
                  >
                    Force Restart
                  </motion.button>
                </div>

                {transcriptionStatus && (
                  <div className="text-sm text-purple-700">{transcriptionStatus}</div>
                )}
              </div>

              {/* Enhanced Live Transcription Display */}
              {isTranscriptionEnabled && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-4 p-4 bg-white/90 backdrop-blur-sm border border-purple-300/50 rounded-2xl shadow-lg"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                        className="w-3 h-3 bg-red-500 rounded-full"
                      />
                      <span className="text-sm font-bold text-purple-600 tracking-wide">
                        LIVE TRANSCRIPTION
                      </span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">
                        {sourceLanguage.toUpperCase()}
                      </span>
                    </div>
                    {currentTranscription && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full"
                      >
                        ✓ Active
                      </motion.div>
                    )}
                  </div>
                  <div className="min-h-[80px] p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200/50 relative overflow-hidden">
                    {currentTranscription ? (
                      <motion.p
                        key={currentTranscription}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-base text-gray-800 leading-relaxed font-medium"
                      >
                        {currentTranscription}
                      </motion.p>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <motion.p
                          animate={{ opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="text-sm text-gray-400 italic flex items-center space-x-2"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                            />
                          </svg>
                          <span>Waiting for speech...</span>
                        </motion.p>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}

          <AnimatePresence>
            {isStreaming && (
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 20, scale: 0.95 }}
                transition={{ duration: 0.4, ease: 'easeOut' }}
                className="mt-6 p-6 bg-gradient-to-br from-emerald-50/90 via-green-50/80 to-teal-50/90 backdrop-blur-sm border border-emerald-200/50 rounded-3xl shadow-lg relative overflow-hidden"
              >
                {/* Background decoration */}
                <div className="absolute top-0 left-0 w-24 h-24 bg-gradient-to-br from-emerald-200/40 to-green-200/30 rounded-full blur-xl transform -translate-x-8 -translate-y-8" />

                <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      animate={{
                        scale: livekitConnected ? [1, 1.2, 1] : 1,
                        boxShadow: livekitConnected
                          ? [
                              '0 0 0 0 rgba(16, 185, 129, 0.4)',
                              '0 0 0 10px rgba(16, 185, 129, 0)',
                              '0 0 0 0 rgba(16, 185, 129, 0)'
                            ]
                          : '0 0 0 0 rgba(16, 185, 129, 0)'
                      }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="w-4 h-4 bg-emerald-500 rounded-full shadow-lg"
                    />
                    <span className="text-emerald-800 font-bold text-lg tracking-wide">
                      LIVE STREAMING
                    </span>

                    <div className="h-6 w-px bg-emerald-300/50"></div>

                    <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl">
                      <motion.div
                        animate={{
                          scale: microphoneActive ? [1, 1.4, 1] : 1,
                          backgroundColor: microphoneActive ? '#ef4444' : '#9ca3af'
                        }}
                        transition={{ duration: 0.3 }}
                        className="w-3 h-3 rounded-full shadow-sm"
                      />
                      <span className="text-emerald-700 text-sm font-semibold">
                        {microphoneActive ? '🎤 RECORDING' : '🎤 STANDBY'}
                      </span>
                    </div>

                    {isTranscriptionEnabled && (
                      <>
                        <div className="h-6 w-px bg-emerald-300/50"></div>
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center space-x-2 bg-purple-100/80 backdrop-blur-sm px-3 py-2 rounded-xl"
                        >
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                            className="w-3 h-3 border-2 border-purple-500 border-t-transparent rounded-full"
                          />
                          <span className="text-purple-700 text-sm font-semibold">
                            TRANSCRIBING
                          </span>
                        </motion.div>
                      </>
                    )}
                  </div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white/70 backdrop-blur-sm px-4 py-2 rounded-xl shadow-sm"
                  >
                    <p className="text-emerald-700 text-sm font-medium">{streamStatus}</p>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {isStreaming ? (
          <div className="flex flex-col xl:flex-row gap-6 relative">
            {/* Main Video Area */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="flex-grow"
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden relative">
                <div
                  className="bg-gradient-to-r from-gray-900 to-black"
                  style={{ aspectRatio: '16/9' }}
                >
                  <AnimatePresence mode="wait">
                    {!isScreenSharing ? (
                      <motion.div
                        key="camera-view"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex items-center justify-center h-full text-white p-8 relative"
                      >
                        <div className="text-center space-y-6">
                          <motion.div
                            initial={{ scale: 0.8 }}
                            animate={{ scale: 1 }}
                            className="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto"
                          >
                            <svg
                              className="w-10 h-10"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </motion.div>
                          <h3 className="text-xl font-semibold">Ready to Share Your Screen</h3>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium"
                            onClick={startScreenShare}
                          >
                            Start Screen Share
                          </motion.button>
                        </div>

                        {/* Live Transcription Overlay - Camera View */}
                        {isTranscriptionEnabled && currentTranscription && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-gray-300">
                                LIVE TRANSCRIPTION
                              </span>
                            </div>
                            <p className="text-sm leading-relaxed">{currentTranscription}</p>
                          </motion.div>
                        )}
                      </motion.div>
                    ) : (
                      <motion.div
                        key="screen-share"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="relative w-full h-full"
                      >
                        <video
                          ref={screenVideoRef}
                          autoPlay
                          playsInline
                          className="w-full h-full object-contain bg-black"
                        />
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className="absolute bottom-6 right-6 w-56 h-40 bg-black rounded-2xl overflow-hidden border-4 border-white/20 shadow-2xl"
                        >
                          <video
                            ref={pipCameraRef}
                            autoPlay
                            playsInline
                            muted
                            className="w-full h-full object-cover"
                          />
                        </motion.div>
                        <div className="absolute top-6 left-6">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={stopScreenShare}
                            className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl font-medium"
                          >
                            Stop Screen Share
                          </motion.button>
                        </div>

                        {/* Live Transcription Overlay - Screen Share View */}
                        {isTranscriptionEnabled && currentTranscription && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-gray-300">
                                LIVE TRANSCRIPTION
                              </span>
                            </div>
                            <p className="text-sm leading-relaxed">{currentTranscription}</p>
                          </motion.div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>

            {/* Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="w-full xl:w-96 flex-shrink-0 space-y-6"
            >
              {/* Camera Preview */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  {isScreenSharing ? 'Camera (PiP Mode)' : 'Camera Preview'}
                </h3>
                <motion.div
                  className="bg-black rounded-xl overflow-hidden"
                  style={{ aspectRatio: '4/3' }}
                >
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                  />
                </motion.div>
              </div>

              {/* Tabbed Info Panel */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
                {/* Tab Buttons */}
                <div className="flex border-b border-gray-200">
                  <button
                    onClick={() => setActiveSidebarTab('status')}
                    className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors ${
                      activeSidebarTab === 'status'
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    <span>Status</span>
                  </button>
                  <button
                    onClick={() => setActiveSidebarTab('viewers')}
                    className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors relative ${
                      activeSidebarTab === 'viewers'
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                    <span>Viewers</span>
                    <span className="absolute top-3 right-3 bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-bold">
                      {joinedViewers.length}
                    </span>
                  </button>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  <AnimatePresence mode="wait">
                    {activeSidebarTab === 'status' && (
                      <motion.div
                        key="status"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">LiveKit</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                livekitConnected
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {livekitConnected ? 'Connected' : 'Disconnected'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Camera</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                cameraPermissionGranted
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {cameraPermissionGranted ? 'Ready' : 'Not Ready'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-orange-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Screen Share</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                isScreenSharing
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {isScreenSharing ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Chat</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                socketConnected
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {socketConnected ? 'Connected' : 'Disconnected'}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    )}
                    {activeSidebarTab === 'viewers' && (
                      <motion.div
                        key="viewers"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {joinedViewers.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">No viewers yet.</div>
                          ) : (
                            joinedViewers.map((viewer) => (
                              <div
                                key={viewer.viewer_id}
                                className="flex items-center justify-between p-3 bg-gray-50 rounded-xl"
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                                    {viewer.viewer_name?.charAt(0)?.toUpperCase() || 'U'}
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-800">
                                      {viewer.viewer_name}
                                    </p>
                                    <span
                                      className={`text-xs px-2 py-0.5 rounded-full ${getRoleColor(
                                        viewer.user_role
                                      )} bg-opacity-10 bg-current`}
                                    >
                                      {getRoleBadge(viewer.user_role)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>

            {/* Fixed Chat Button - Bottom Right Corner */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleChat}
              className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white p-4 rounded-full shadow-2xl transition-all duration-200 z-50"
              title="Toggle Chat"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                />
              </svg>
              <AnimatePresence>
                {unreadMessages > 0 && !isChatOpen && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold border-2 border-white"
                  >
                    {unreadMessages}
                  </motion.span>
                )}
              </AnimatePresence>
            </motion.button>

            {/* Chat Overlay - Fixed Bottom Right */}
            <AnimatePresence>
              {isChatOpen && (
                <motion.div
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 20, scale: 0.95 }}
                  transition={{ duration: 0.3 }}
                  className="fixed bottom-24 right-6 w-96 h-[500px] bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 flex flex-col z-40"
                >
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Live Chat</h3>
                    <motion.button
                      whileHover={{ scale: 1.1, rotate: 90 }}
                      onClick={toggleChat}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </motion.button>
                  </div>
                  <div className="flex-grow p-4 overflow-y-auto space-y-3">
                    {chatMessages.length === 0 ? (
                      <div className="flex items-center justify-center h-full text-center text-gray-500">
                        No messages yet.
                      </div>
                    ) : (
                      chatMessages.map((message, index) => (
                        <motion.div
                          key={`${message.id || 'msg'}-${index}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-white rounded-xl p-3 shadow-sm border"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <span
                                className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}
                              >
                                {message.sender_name}
                              </span>
                              <p className="text-sm text-gray-800">{message.message}</p>
                            </div>
                            <span className="text-xs text-gray-400 ml-2">
                              {formatMessageTime(message.timestamp)}
                            </span>
                          </div>
                        </motion.div>
                      ))
                    )}
                  </div>
                  <div className="p-4 border-t border-gray-200 flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type a message..."
                      className="flex-1 px-4 py-2 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!socketConnected}
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={sendChatMessage}
                      disabled={!newMessage.trim() || !socketConnected}
                      className="p-3 bg-blue-600 text-white rounded-xl disabled:bg-gray-400"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Quiz Upload Modal */}
            <AnimatePresence>
              {showQuizUpload && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
                  onClick={() => setShowQuizUpload(false)}
                >
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.9, opacity: 0 }}
                    className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold text-gray-800">Generate Quiz</h3>
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        onClick={() => setShowQuizUpload(false)}
                        className="text-gray-500 hover:text-gray-800"
                      >
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </motion.button>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Upload PDF File
                        </label>
                        <input
                          type="file"
                          accept=".pdf"
                          onChange={handleQuizFileChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        {quizFile && (
                          <p className="text-sm text-green-600 mt-2">✅ {quizFile.name} selected</p>
                        )}
                      </div>

                      <div className="flex space-x-3 pt-4">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleUploadQuiz}
                          disabled={!quizFile || isUploadingQuiz}
                          className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isUploadingQuiz ? (
                            <span className="flex items-center justify-center space-x-2">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ repeat: Infinity, duration: 1 }}
                                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                              />
                              <span>Uploading...</span>
                            </span>
                          ) : (
                            'Generate Quiz'
                          )}
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setShowQuizUpload(false)}
                          className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl hover:bg-gray-300 transition-all duration-200 font-medium"
                        >
                          Cancel
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ) : (
          /* Enhanced Start Streaming Screen */
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30 p-16 text-center max-w-3xl mx-auto relative overflow-hidden"
          >
            {/* Background decorations */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-blue-200/30 to-indigo-200/20 rounded-full blur-3xl transform translate-x-20 -translate-y-20" />
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-200/30 to-pink-200/20 rounded-full blur-3xl transform -translate-x-16 translate-y-16" />

            <div className="relative z-10">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className="w-32 h-32 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl"
              >
                <motion.svg
                  className="w-16 h-16 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </motion.svg>
              </motion.div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-6"
              >
                Ready to Go Live? ✨
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="text-gray-600 mb-10 text-xl font-medium"
              >
                {streamStatus}
              </motion.p>

              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mb-8 p-6 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-2xl text-red-700 shadow-lg"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span className="font-medium">{error}</span>
                  </div>
                </motion.div>
              )}

              <motion.button
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                onClick={startStreaming}
                className="px-16 py-5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white rounded-3xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <span className="relative flex items-center justify-center space-x-3">
                  <motion.svg
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z"
                    />
                  </motion.svg>
                  <span>Start Live Streaming</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                </span>
              </motion.button>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="mt-8 text-sm text-gray-500 space-y-2"
              >
                <p>🎥 Camera and microphone will be activated</p>
                <p>📡 Live streaming will begin immediately</p>
                <p>💬 Chat and transcription features available</p>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default TeacherLiveStreaming;